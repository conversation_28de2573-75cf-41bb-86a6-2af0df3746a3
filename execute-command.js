import { chromium } from 'playwright';
import fs from 'fs';
import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';

async function executeCommand() {
  // 检查cookie文件是否存在
  if (!fs.existsSync(config.cookieFile)) {
    console.error(`<PERSON>ie文件不存在: ${config.cookieFile}`);
    console.log('请先运行 npm run login 进行登录');
    return;
  }

  console.log('启动浏览器...');
  const browser = await chromium.launch(config.browserOptions);
  const context = await browser.newContext();

  try {
    // 读取并设置cookies
    const cookies = JSON.parse(fs.readFileSync(config.cookieFile, 'utf8'));
    await context.addCookies(cookies);
    console.log(`已加载 ${cookies.length} 个cookies`);

    const page = await context.newPage();

    console.log('导航到WebIDE页面...');
    await page.goto(config.webideUrl);

    // 等待页面加载
    await page.waitForTimeout(config.waitTimes.pageLoad);

    console.log('当前页面URL:', page.url());
    console.log('页面标题:', await page.title());

    // 检查是否成功登录
    try {
      await page.waitForSelector('.monaco-grid-view', {
        timeout: 10000
      });
      console.log('成功进入WebIDE界面');
    } catch (error) {
      console.log('警告: 未检测到编辑器界面，可能需要重新登录');
    }

    console.log('尝试打开终端 (Ctrl+~)...');

    // 确保页面获得焦点
    await page.click('body');
    await page.waitForTimeout(500);

    // 按下 Ctrl+~ 打开终端
    await page.keyboard.press('Control+`');

    // 等待终端打开
    await page.waitForTimeout(config.waitTimes.terminalOpen);

    // 尝试多种方式查找终端
    const terminalSelectors = [
      '.terminal',
      '.xterm',
      '.console',
      '.terminal-container',
      '.xterm-screen',
      '.monaco-workbench .part.panel .terminal',
      '[data-testid="terminal"]',
      '.integrated-terminal'
    ];

    let terminalFound = false;
    let terminalElement = null;

    for (const selector of terminalSelectors) {
      try {
        terminalElement = await page.waitForSelector(selector, { timeout: 2000 });
        if (terminalElement) {
          console.log(`找到终端元素: ${selector}`);
          terminalFound = true;
          break;
        }
      } catch (error) {
        // 继续尝试下一个选择器
      }
    }

    if (!terminalFound) {
      console.log('未找到终端元素，尝试直接输入命令...');
      // 如果找不到终端元素，直接尝试输入
    } else {
      // 点击终端区域确保焦点
      await terminalElement.click();
      await page.waitForTimeout(500);
    }

    console.log(`执行命令: ${config.command}`);

    // 输入命令
    await page.keyboard.type(config.command);
    await page.waitForTimeout(500);

    // 按回车执行命令
    await page.keyboard.press('Enter');

    // 等待命令执行
    await page.waitForTimeout(config.waitTimes.commandExecution);

    console.log('命令已执行');

    // 可选：截图保存执行结果
    const screenshotPath = `./screenshot-${Date.now()}.png`;
    await page.screenshot({ path: screenshotPath });
    console.log(`截图已保存: ${screenshotPath}`);

    // 保持浏览器打开一段时间以便查看结果
    if (!config.browserOptions.headless) {
      console.log('浏览器将保持打开5秒以便查看结果...');
      await page.waitForTimeout(5000);
    }

  } catch (error) {
    console.error('执行命令过程中发生错误:', error);
  } finally {
    await browser.close();
    console.log('浏览器已关闭');
  }
}

// 运行命令执行脚本
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  executeCommand().catch(console.error);
}

export { executeCommand };
