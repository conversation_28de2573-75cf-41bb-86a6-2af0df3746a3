import { executeCommand } from './execute-command.js';

class CommandScheduler {
  constructor(intervalMinutes = 30) {
    this.intervalMinutes = intervalMinutes;
    this.intervalMs = intervalMinutes * 60 * 1000;
    this.isRunning = false;
    this.intervalId = null;
  }

  start() {
    if (this.isRunning) {
      console.log('调度器已经在运行中');
      return;
    }

    console.log(`启动定时执行器，间隔: ${this.intervalMinutes} 分钟`);
    this.isRunning = true;

    // 立即执行一次
    this.executeWithErrorHandling();

    // 设置定时执行
    this.intervalId = setInterval(() => {
      this.executeWithErrorHandling();
    }, this.intervalMs);

    console.log('定时执行器已启动');
    console.log('按 Ctrl+C 停止执行器');
  }

  stop() {
    if (!this.isRunning) {
      console.log('调度器未在运行');
      return;
    }

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.isRunning = false;
    console.log('定时执行器已停止');
  }

  async executeWithErrorHandling() {
    const now = new Date().toLocaleString();
    console.log(`\n[${now}] 开始执行命令...`);

    try {
      await executeCommand();
      console.log(`[${now}] 命令执行完成`);
    } catch (error) {
      console.error(`[${now}] 命令执行失败:`, error.message);
    }

    const nextRun = new Date(Date.now() + this.intervalMs).toLocaleString();
    console.log(`下次执行时间: ${nextRun}`);
  }
}

// 命令行参数处理
function parseArgs() {
  const args = process.argv.slice(2);
  let intervalMinutes = 30; // 默认30分钟

  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--interval' || args[i] === '-i') {
      const interval = parseInt(args[i + 1]);
      if (!isNaN(interval) && interval > 0) {
        intervalMinutes = interval;
      }
    }
  }

  return { intervalMinutes };
}

// 主函数
function main() {
  const { intervalMinutes } = parseArgs();
  const scheduler = new CommandScheduler(intervalMinutes);

  // 处理程序退出
  process.on('SIGINT', () => {
    console.log('\n收到退出信号，正在停止调度器...');
    scheduler.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n收到终止信号，正在停止调度器...');
    scheduler.stop();
    process.exit(0);
  });

  // 启动调度器
  scheduler.start();
}

// 运行调度器
main();